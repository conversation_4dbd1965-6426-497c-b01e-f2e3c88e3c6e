import { fileURLToPath, URL } from "node:url";
import vue from "@vitejs/plugin-vue";
import { defineConfig } from "vite";

export default defineConfig({
  css: {
    postcss: {
      plugins: [],
    },
  },
  plugins: [vue()],

  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
      "@components": fileURLToPath(
        new URL("./src/components", import.meta.url)
      ),
      "@views": fileURLToPath(new URL("./src/views", import.meta.url)),
      "@layouts": fileURLToPath(new URL("./src/layouts", import.meta.url)),
      "@lib": fileURLToPath(new URL("./src/lib", import.meta.url)),
      "@pages": fileURLToPath(new URL("./src/pages", import.meta.url)),
      "@55": fileURLToPath(new URL("./src/components/ff", import.meta.url)),
      "@events": fileURLToPath(
        new URL("./src/utils/eventBus.ts", import.meta.url)
      ),
      "@types": fileURLToPath(new URL("./src/types", import.meta.url)),
      "@store-types": fileURLToPath(new URL("./src/stores/types", import.meta.url)),
      "@validation-schemas": fileURLToPath(
        new URL("./src/utils/validation-schemas", import.meta.url)
      ),
      "@utils": fileURLToPath(new URL("./src/utils", import.meta.url)),
    },
  },
});
