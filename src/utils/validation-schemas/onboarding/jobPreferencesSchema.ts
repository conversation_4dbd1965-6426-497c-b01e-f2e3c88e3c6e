import { z } from "zod";
import { toTypedSchema } from "@vee-validate/zod";

// Location object schema to match LocationSearchResult
const locationSchema = z.object({
  placeId: z.string().optional(),
  name: z.string().optional(),
  country: z
    .object({
      code: z.string().optional(),
      name: z.string().optional(),
    })
    .optional(),
  city: z.string().optional(),
  district: z.string().optional(),
  zipCode: z.string().optional(),
  formattedAddress: z.string().min(1, "Please select a location"),
  lanAndlng: z
    .object({
      lat: z.number().optional(),
      lng: z.number().optional(),
    })
    .optional(),
});

// Schema for vee-validate/BForm integration - excluding location (handled manually)
export const jobPreferencesSchema = toTypedSchema(
  z.object({
    dreamJob: z.string().min(2, "Please enter your dream job"),
    preferedLocation: locationSchema,
    experience: z.number().min(0, "Experience must be 0 or greater").optional(),
    workSetup: z.enum(["on-site", "remote", "hybrid"], {
      errorMap: () => ({ message: "Please select a work setup" }),
    }),
    // Salary field - string based on store structure (format: "USD:50000-80000")
    salary: z
      .string()
      .min(1, "Please enter your desired salary")
      .refine(
        (val) => {
          // Handle empty or invalid strings
          if (!val || typeof val !== "string") return false;

          const parts = val.split(":");
          if (parts.length !== 2) return false;

          const rangeParts = parts[1]?.split("-");
          // Ensure both from and to values exist and are not empty
          return (
            rangeParts &&
            rangeParts.length === 2 &&
            rangeParts[0] &&
            rangeParts[1]
          );
        },
        {
          message: "Please provide both a minimum and maximum salary",
        }
      )
      .refine(
        (val) => {
          // Handle empty or invalid strings
          if (!val || typeof val !== "string") return false;

          const parts = val.split(":");
          if (parts.length !== 2 || !parts[1]) return false;

          const rangeParts = parts[1].split("-");
          if (rangeParts.length !== 2 || !rangeParts[0]) return false;

          const fromAmount = parseFloat(rangeParts[0].replace(/,/g, ""));
          // Check if fromAmount is a positive number
          return !isNaN(fromAmount) && fromAmount > 0;
        },
        {
          message: "Minimum salary must be a valid number greater than 0",
        }
      )
      .refine(
        (val) => {
          // Handle empty or invalid strings
          if (!val || typeof val !== "string") return false;

          const parts = val.split(":");
          if (parts.length !== 2 || !parts[1]) return false;

          const rangeParts = parts[1].split("-");
          if (rangeParts.length !== 2 || !rangeParts[0] || !rangeParts[1])
            return false;

          const fromAmount = parseFloat(rangeParts[0].replace(/,/g, ""));
          const toAmount = parseFloat(rangeParts[1].replace(/,/g, ""));

          // Check if both amounts are valid numbers and toAmount >= fromAmount
          return (
            !isNaN(fromAmount) && !isNaN(toAmount) && fromAmount <= toAmount
          );
        },
        {
          message:
            "Maximum salary must be greater than or equal to the minimum",
        }
      ),
  })
);
