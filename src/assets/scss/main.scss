@use "./mixin" as *;

@use "./variables" as *;
@use "./typography";
@use "./tooltip";
@use "./popover";

@use "./helpers";
@use "./modal";
@use "./form";
@use "./colors";

@use "./buttons";

@include gap-classes(10);
@include margin-classes(10);
@include spacing-classes(10);
@include absolute-classes(10);

.pac-container {
  z-index: 10000 !important;
}

* ⁠ {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  height: 100%;
}

.p-skeleton {
  border-radius: 12px;
}

body {
  // -webkit-font-smoothing: antialiased;
  // -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100vw;
  scrollbar-width: thin; /* Width of the scrollbar */
  scrollbar-color: #ccc transparent; /* Color of the scrollbar thumb and track */
  overflow-y: scroll;
  font-family: var(--font-1);
  color: #5e5e5e;
  &.no-scroll {
  }
}

.container {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  max-width: 1900px !important;
  padding-left: 80px;
  padding-right: 80px;
  margin: 0 auto;

  @include respond-below(sm) {
    padding-left: 20px;
    padding-right: 20px;
  }
}

main.auth-layout {
  position: fixed;
  height: 100%;
  width: 100%;

  .auth-container {
    min-height: 100%;
    display: flex;
    width: 400px;
    min-width: 400px;
    align-items: center;
    margin: 0 auto;
    justify-content: center;
    .auth-wrapper {
      width: 100%;
    }
  }
}
