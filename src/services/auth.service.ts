import { api } from "@/lib/server";
import type {
  AuthResponse,
  AuthErrorLog,
  User,
} from "@/stores/types/auth.types";
import { useAuthStore } from "@/stores/auth.store";
import type { ProviderLoginTypes, RoleShortened } from "@/types/general.types";
import router from "@/router";
import type { ApiResponse } from "@/types/general.types";
import { ErrorHandler } from "@/lib/errorHandler";
interface LoginCredentials {
  email: string;
  password: string;
}

interface ForgetPassword {
  email: string;
  captchaToken: string;
}

interface ResetToken {
  token: string;
}

interface finishResetPassword {
  token: string;
  newPassword: string;
  email: string;
}

export class AuthService {
  private static readonly BASE_PATH = "/api/auth";
  private static readonly BASE_PATH_USER = "/api/user";

  static async login(
    credentials: LoginCredentials
  ): Promise<ApiResponse<User>> {
   
     try {
      const { data } = await api.post<AuthResponse>(
        `${this.BASE_PATH}/login`,
        credentials
      );
      if (data.success) {
        const authStore = useAuthStore();
        authStore.setAuthData(data.data);
        if (data.data.onboardingCompleted) {
          router.push({ name: "dashboard" });
        } else {
          router.push({ name: "onboarding" });
        }
      }
      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::login", error);
    }

  }

  static async me(): Promise<ApiResponse<User>> {
    try {
      const { data } = await api.get<ApiResponse<User>>(
        `${this.BASE_PATH_USER}/profile/me`
      );
      if (data.success) {
        const authStore = useAuthStore();
        // Handle both cases where user might be directly in data.data or nested in data.data.user
        const userData =
          "user" in data.data ? (data.data.user as User) : (data.data as User);
        authStore.setAuthData(userData);
        if (userData.onboardingCompleted) {
          router.push({ name: "dashboard" });
        } else {
          console.log("onboarding");
          router.push({ name: "onboarding" });
        }
      } else {
        console.error("Me endpoint returned unsuccessful response:", data);
        throw new Error(`Me endpoint failed: ${data.message}`);
      }

      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::me", error);
    }
  }

  static async loginWithProvider(
    provider: ProviderLoginTypes,
    role: RoleShortened
  ): Promise<void> {
    try {
      window.location.href = `${
        import.meta.env.VITE_SERVER_URL
      }/api/auth/provider/${provider}?r=${role}`;
    } catch (error) {
      ErrorHandler.handle("AuthService::loginWithProvider", error);
    }
  }

  static async refresh_token(): Promise<AuthResponse> {
    try {
      const { data } = await api.post<AuthResponse>(
        `${this.BASE_PATH}/refresh-token`
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::refresh_token", error);
    }
  }

  static async initForgetPassword(
    credentials: ForgetPassword
  ): Promise<AuthResponse> {
    try {
      const { data } = await api.post<AuthResponse>(
        `${this.BASE_PATH}/forgot-password`,
        credentials
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::initForgetPassword", error);
    }
  }

  static async confirmResetToken(token: ResetToken): Promise<AuthResponse> {
    try {
      const { data } = await api.post<AuthResponse>(
        `${this.BASE_PATH}/confirm-reset-token`,
        token
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::confirmResetToken", error);
    }
  }

  static async finishResetPassword(
    resetData: finishResetPassword
  ): Promise<AuthResponse> {
    try {
      const { data } = await api.post<AuthResponse>(
        `${this.BASE_PATH}/reset-password`,
        resetData
      );
      return data;
    } catch (error) {
      ErrorHandler.handle("AuthService::finishResetPassword", error);
    }
  }

  static async logout(): Promise<void> {
    try {
      const authStore = useAuthStore();
      await api.post<AuthResponse>(`${this.BASE_PATH}/logout`);
      authStore.clearAuth();
      router.push({ name: "login" });
    } catch (error) {
      ErrorHandler.handle("AuthService::logout", error);
    }
  }
}
