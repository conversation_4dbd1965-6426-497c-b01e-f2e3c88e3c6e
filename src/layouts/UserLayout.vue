<script setup lang="ts">


import { KeyRound} from 'lucide-vue-next'
import { AuthService } from "@/services";
import { useAuthStore } from "@/stores/auth.store";
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();


// const handleLogout = async () => {
//   try {
//     await AuthService.logout();
//     router.push("/auth/login");
//   } catch (error) {
//     console.error("Logout failed:", error);
//   }
// };

</script>

<template>
   <main class="">
    <RouterView />
    </main>
</template>
<style scoped lang="scss">
</style>