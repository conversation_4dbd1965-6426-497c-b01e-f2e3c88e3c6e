<template>
  <div class="onboarding-container">
    <!-- Header -->
    <header class="onboarding-header">
      <div class="header-content">
        <div class="logo-section">
          <LogoImage size="md" />
        </div>

        
        <div class="header-actions">
          <B_Button type="secondary" size="md" :is-box="false" leftIcon="help">
            Help
          </B_Button>
          <B_Button type="secondary" size="md" :is-box="true" leftIcon="logout" @click="logoutUser">
          </B_Button>
          <div class="user-avatar">
        
            <!-- <img src="/api/placeholder/32/32" alt="User" class="avatar-img" /> -->
            <B_Avatar size="sm" :user="authStore.user || undefined" />
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <div class="onboarding-main">
      <!-- Sidebar with Steps -->
      <aside class="onboarding-sidebar">
        <StepsVertical
          :steps="registrationStore.steps"
          :current-step="registrationStore.currentStep"
          :show-card="true"
        />
      </aside>

      <!-- Main Form Area -->
      <main class="onboarding-content">
        <div class="form-container">
          <!-- Step 1: Personal Information -->
          <div
            v-if="registrationStore.currentStep === 'personal-info'"
            class="step-form"
          >
            <PersonalInfoStep
              ref="personalInfoStepRef"
              @validation-change="(isValid: boolean) => (isFormValid = isValid)"
            />
          </div>

          <!-- Step 2: Job Preferences -->
          <div
            v-else-if="registrationStore.currentStep === 'job-preferences'"
            class="step-form"
          >
            <JobPreferencesStep
              ref="jobPreferencesStepRef"
              @validation-change="(isValid: boolean) => (isFormValid = isValid)"
            />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import B_Button from "@/components/talentflow/button.vue";
import { logoutUser } from "@/utils/helpers"
import LogoImage from "@/components/ff/logo-image.vue";
import StepsVertical from "@/components/ff/steps-vertical.vue";
import B_Avatar from "@/components/ff/avatar.vue";
import { useAuthStore } from "@/stores/auth.store";
import { useRegistrationStore } from "@/stores/registration.store";

import PersonalInfoStep from "@/components/talentflow/steps/personal-info-step.vue";
import JobPreferencesStep from "@/components/talentflow/steps/job-preferences-step.vue";

const authStore = useAuthStore();
const registrationStore = useRegistrationStore();

// Component refs
const personalInfoStepRef = ref();
const jobPreferencesStepRef = ref();

// Form validity state
const isFormValid = ref(false);

// Form validity state (kept for future use if needed)
// No longer need centralized button logic since each step handles its own buttons
</script>

<style scoped lang="scss">
@use "../../assets/scss/mixin" as *;

.onboarding-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  position: relative;

  &::before {
    content: "";
    position: absolute;
    width: 482px;
    height: 480px;

    right: 35%;
    top: -174px;
    background: radial-gradient(
      117.79% 50% at 50% 50%,
      #ddfeef 0%,
      #f1f4ff 100%
    );
    filter: blur(42px);
    z-index: 0;
    pointer-events: none;
  }

  > * {
    position: relative;
    z-index: 1;
  }
}

// Header styles
.onboarding-header {
  padding: 1rem 0;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .logo-text {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .user-avatar {
      order: 999; // Ensure avatar appears last

      .avatar-img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 1px solid #e5e7eb;
        object-fit: cover;
      }
    }
  }
}

// Main content layout
.onboarding-main {
  flex: 1;
  display: flex;
  align-items: flex-start;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

// Sidebar styles
.onboarding-sidebar {
  width: 380px;
  padding: 2rem;
  margin-top: 4rem;
  height: fit-content;
  position: sticky;
  top: 2rem;

  :deep(.steps-vertical-container) {
    position: static;
  }
}

// Content area styles
.onboarding-content {
  flex: 1;
  padding: 2rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;

  .form-container {
    width: 100%;
  }

  .step-form {
    border-radius: 12px;
    padding: 3rem;
    padding-bottom: 0 !important;

    .form-actions {
      padding: 0 3rem !important;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 5px;

      & > :last-child {
        flex: 1;
        text-align: right;
      }
    }
  }

  .form-header {
    margin-bottom: 2rem;
    text-align: center;

    .form-title {
      font-size: 2rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
    }

    .form-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      line-height: 1.6;
    }
  }

  .onboarding-form {
    .form-group {
      margin-bottom: 1.5rem;

      .form-help-text-small {
        font-size: 0.75rem;
        color: #6b7280;
        margin-top: 0.25rem;
        margin-bottom: 0;
      }
    }
  }

  // Form actions styles (available globally in onboarding-content)
  .form-actions {
    padding-top: 0 !important;
    padding-bottom: 0 !important;

    .form-actions-row {
      display: flex;
      gap: 1rem;
      align-items: center;

      .back-btn {
        flex-shrink: 0;
        min-width: auto;
      }

      .submit-btn {
        flex: 1;
      }
    }
  }

  .coming-soon {
    text-align: center;
    padding: 2rem;

    p {
      font-size: 1.125rem;
      color: #6b7280;
      margin-bottom: 2rem;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .onboarding-main {
    flex-direction: column;
  }

  .onboarding-sidebar {
    width: 100%;
    padding: 1rem;

    .steps-list {
      display: flex;
      gap: 1rem;
      overflow-x: auto;
      position: static;
    }

    .step-item {
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-width: 200px;
      border-bottom: none;
      border-right: 1px solid #f3f4f6;

      &:last-child {
        border-right: none;
      }

      .step-content {
        .step-title {
          font-size: 0.875rem;
        }

        .step-description {
          display: none;
        }
      }
    }
  }

  .onboarding-content {
    padding: 1rem;

    .step-form {
      padding: 2rem 1.5rem;
    }

    .form-header {
      .form-title {
        font-size: 1.5rem;
      }

      .form-subtitle {
        font-size: 1rem;
      }
    }
  }

  .header-content {
    padding: 0 1rem !important;
  }
}
</style>
