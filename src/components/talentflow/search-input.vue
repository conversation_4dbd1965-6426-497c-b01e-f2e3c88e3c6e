<template>
  <div
    :class="[
      'ff_input relative',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <label :for="id" v-if="label">{{ label }}</label>

    <div
      class="ff-input-container"
      :class="{
        'input-with-left-icon': leftIcon,
        'input-with-left-leading-text': leftLeadingText,
        'input-with-right-leading-text': rightLeadingText,
        'input-with-right-icon': rightIcon || isLoading,
        'ff-input-with-icon': leftIcon || rightIcon || isLoading,
      }"
    >
      <span
        v-if="leftIcon && !leftLeadingText"
        class="icon_item left-icon"
        @click="onIconClick('left')"
      >
        <component
          v-if="iconMap[leftIcon]"
          :is="iconMap[leftIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="leftLeadingText" class="left-leading-text">
        {{ leftLeadingText }}
      </span>

      <input
        :autocomplete="autocomplete"
        :type="inputType || type"
        v-bind="
          maxNumber !== undefined
            ? { max: maxNumber, 'v-limit-length': maxNumber }
            : {}
        "
        ref="inputElement"
        :id="id"
        :placeholder="placeholder"
        :value="internalValue"
        @input="updateValue"
        @focus="onFocus"
        @blur="emitBlur"
        @keydown="onKeyDown"
        @keydown.tab="onTabPressed"
        @keydown.enter="onEnterPressed"
        :disabled="disabled"
      />

      <span
        v-if="rightIcon || isLoading"
        class="icon_item right-icon"
        @click="onIconClick('right')"
        :class="{ cursor: rightIconClickable }"
      >
        <Loader v-if="isLoading" size="lg" />
        <component
          v-else-if="rightIcon && iconMap[rightIcon]"
          :is="iconMap[rightIcon]"
          v-bind="defaultIconProps"
        />
      </span>
      <span v-if="rightLeadingText" class="right-leading-text">
        {{ rightLeadingText }}
      </span>
    </div>

    <!-- Suggestions Dropdown -->
    <div
      v-if="showSuggestions && suggestions.length > 0"
      class="suggestions-dropdown"
    >
      <div
        v-for="(suggestion, index) in suggestions"
        :key="index"
        class="suggestion-item"
        @click="onSuggestionClick(suggestion)"
      >
        <template v-if="searchType == 'country'">
          {{ suggestion.formattedAddress }}
        </template>
        <template v-else>
          {{ suggestion.name || suggestion.label || suggestion }}
        </template>
      </div>
    </div>

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import { ref, watch, computed, inject } from "vue";
import { iconMap, type IconName } from "@55/iconMap";
import Loader from "@55/loader.vue";
import { useField } from "vee-validate";

import { AppService } from "@/services/app.service";
import type { Job, JobResponse } from "@/types/job.types";
import type { Place, PlaceResponse } from "@/types/place.types";
import type { ApiResponse } from "@/types/general.types";

// import { email } from '@/assets/icons/email'
const inputElement = ref<HTMLElement | null>(null);
const props = defineProps({
  modelValue: String,
  value: String,
  hasRightLabel: Boolean,
  rightLabelText: String,
  rightLabelDirection: String,
  leftIcon: {
    type: String as PropType<IconName>,
    required: false,
  },
  leftLeadingText: String,
  rightLeadingText: String,
  max: {
    type: [String, Number],
    required: false,
  },
  rightIcon: {
    type: String as PropType<IconName>,
    required: false,
  },

  rightIconClickable: {
    type: Boolean,
    default: false,
  },
  label: String,
  type: {
    type: String,
    default: "text",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,

  // Input type for different data types
  inputType: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "text",
  },
  // Form integration props
  name: {
    type: String,
    required: false,
  },
  searchType: {
    type: String as PropType<"country" | "jobPosition">,
    default: "country",
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

// Inject form context if available
const formContext = inject("formContext", null);

const minCharacters = ref(3);
// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const internalValueRef = shouldUseVeeValidate ? null : ref(props.modelValue);

const internalError = ref<string>("");

// Computed property for the current value
const internalValue = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return veeField.value.value;
  }
  return internalValueRef?.value;
});

// Get validation functions

const moneyValue = ref(props.modelValue || props.value || "");

const onFocus = () => {};

const suggestions = ref<any[]>([]);
const isLoading = ref(false);
const showSuggestions = ref(false);

// Custom debounce function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      func(...args);
    };

    if (timeout) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}

const debouncedSearch = debounce(async (query: string) => {
  if (query.length < minCharacters.value) {
    suggestions.value = [];
    return;
  }

  try {
    isLoading.value = true;
    let response:
      | ApiResponse<JobResponse | PlaceResponse | Job | Place>
      | undefined;
    if (props.searchType === "jobPosition") {
      response = await AppService.searchJobPosition({
        q: query,
        type: "dreamRoles",
      });
    } else if (props.searchType === "country") {
      response = await AppService.searchPlace({ q: query, type: "places" });
    }

    if (response?.data) {
      if ("results" in response.data) {
        suggestions.value = response.data.results;
      } else {
        suggestions.value = [response.data];
      }
    }
  } catch (error) {
    console.error("Search error:", error);
    suggestions.value = [];
  } finally {
    isLoading.value = false;
  }
}, 300);

const updateValue = (event: Event) => {
  const newValue = (event.target as HTMLInputElement).value;

  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = newValue;
  } else {
    if (internalValueRef) {
      internalValueRef.value = newValue;
    }

    if (internalError.value && newValue.length > 0) {
      internalError.value = "";
    }
  }

  if (props.searchType) {
    debouncedSearch(newValue);
    showSuggestions.value = true;
  }

  emit("update:modelValue", newValue);
  emit("change", newValue);
};

const onSuggestionClick = (suggestion: any) => {
  // For location searches, use formattedAddress; for other searches, use name/label
  const value =
    props.searchType === "country"
      ? suggestion.formattedAddress ||
        suggestion.name ||
        suggestion.label ||
        suggestion
      : suggestion.name || suggestion.label || suggestion;

  if (shouldUseVeeValidate && veeField) {
    veeField.value.value = value;
  } else {
    if (internalValueRef) {
      internalValueRef.value = value;
    }
  }
  suggestions.value = [];
  showSuggestions.value = false;
  emit("update:modelValue", value);
  emit("change", value);

  // For location searches, also emit the full suggestion object
  if (props.searchType === "country") {
    emit("select", suggestion);
  }
};

// Handle Tab key to accept inline suggestion
const onKeyDown = (event: KeyboardEvent) => {
  // if (
  //   event.key === "Tab" &&
  //   inlineSuggestion.value &&
  //   props.showInlineSuggestion
  // ) {
  //   event.preventDefault();
  //   emit("update:modelValue", newValue);
  //   emit("change", newValue);
  // }
};

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "iconClick",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
  "rightLabelClicked",
  "select",
]);

const rightLabelClicked = () => {
  emit("rightLabelClicked");
};

const onIconClick = (position: "left" | "right") => {
  emit("iconClick", position);
};
const emitBlur = () => {
  emit("blur");
};
const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};
const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (shouldUseVeeValidate && veeField) {
      veeField.value.value = newValue || "";
    } else {
      if (internalValueRef) {
        internalValueRef.value = newValue;
      }
    }
  }
);

const maxNumber = computed(() => {
  const n = Number(props.max);
  return isNaN(n) ? undefined : n;
});

const defaultIconProps = {
  size: 20,
};

defineExpose({ focus, clearValidationError });
</script>

<style lang="scss" scoped>
.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  max-height: 240px;
  overflow-y: auto;
  margin-top: 0.25rem;
  padding: 0.5rem;

  .suggestion-item {
    padding: 6px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 0.5rem;
    font-size: 1rem;
    line-height: 1.5;
    font-weight: 400;
    color: #111827;
    margin-bottom: 0.125rem;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background-color: #f1f5f9;
      color: #1e293b;
    }
  }
}
</style>
