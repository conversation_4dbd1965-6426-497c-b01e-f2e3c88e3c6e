<template>
  <div
    :class="[
      'ff_input',
      extraClass,
      { 'has-error': hasError, disabled: disabled },
    ]"
  >
    <label :for="id" v-if="label">{{ label }}</label>

    <div
      class="ff-input-container"
      :class="{
        'input-with-left-icon': showCountrySelector,
        'has-country-dropdown': dropdownOpen,
      }"
    >
      <!-- Country Selector as Left Icon -->
      <span
        v-if="showCountrySelector"
        class="icon_item left-icon country-selector-icon cursor"
        @click="toggleCountryDropdown"
      >
        <div class="country-display">
          <span class="country-flag">{{
            selectedCountryData?.flag || "🌍"
          }}</span>
          <span class="country-code"
            >+{{ selectedCountryData?.dialCode?.replace("+", "") || "1" }}</span
          >
        </div>
      </span>

      <input
        :autocomplete="autocomplete"
        :type="type"
        ref="inputElement"
        :id="id"
        :placeholder="placeholder"
        :value="displayValue"
        @input="updateValue"
        @focus="onFocus"
        @blur="emitBlur"
        @keydown="onKeyDown"
        @keydown.tab="onTabPressed"
        @keydown.enter="onEnterPressed"
        :disabled="disabled"
      />

      <!-- Country Dropdown -->
      <div v-if="dropdownOpen && showCountrySelector" class="country-dropdown">
        <div class="search-container">
          <input
            ref="searchInput"
            v-model="searchQuery"
            placeholder="Search countries..."
            class="country-search"
            @keydown.escape="closeDropdown"
          />
        </div>
        <div class="countries-list">
          <div
            v-for="country in filteredCountries"
            :key="country.code"
            class="country-item"
            @click="selectCountry(country)"
          >
            <span class="country-flag">{{ country.flag }}</span>
            <span class="country-name">{{ country.name }}</span>
            <span class="country-dial-code">{{ country.dialCode }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="input_errors" v-if="displayError">
      <p class="error">{{ displayError }}</p>
    </div>
    <template v-else-if="hint">
      <div class="input_errors" v-if="hint">
        <p class="hint">{{ hint }}</p>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import {
  ref,
  watch,
  computed,
  inject,
  onMounted,
  nextTick,
  onUnmounted,
} from "vue";
import {
  parsePhoneNumberFromString,
  formatIncompletePhoneNumber,
} from "libphonenumber-js";
import type { CountryCode } from "libphonenumber-js";
import { countries } from "country-codes-flags-phone-codes";
import { useField } from "vee-validate";

const inputElement = ref<HTMLElement | null>(null);
const searchInput = ref<HTMLInputElement | null>(null);
const dropdownOpen = ref(false);
const searchQuery = ref("");

const props = defineProps({
  modelValue: String,
  value: String,
  label: String,
  type: {
    type: String as PropType<
      "text" | "tel" | "email" | "number" | "password" | "url"
    >,
    default: "tel",
  },
  hint: String,
  id: String,
  placeholder: String,
  disabled: Boolean,
  autocomplete: String,
  extraClass: String,
  errorMessage: String,
  defaultCountry: {
    type: String as PropType<CountryCode>,
    default: "US",
  },
  formatAsYouType: {
    type: Boolean,
    default: true,
  },
  showCountrySelector: {
    type: Boolean,
    default: true,
  },

  // Validation props
  validationType: {
    type: String as PropType<
      "email" | "password" | "phone" | "required" | "none"
    >,
    default: "phone",
  },
  validationMessage: String,

  // Form integration props
  name: {
    type: String,
    required: false,
  },
});

function focus() {
  inputElement.value?.focus();
}

function clearValidationError() {
  internalError.value = "";
}

// Convert country-codes-flags-phone-codes format to our format
const availableCountries = computed(() => {
  return countries.map((country) => ({
    name: country.name,
    code: country.code as CountryCode,
    dialCode: country.dialCode,
    flag: country.flag,
  }));
});

// Selected country state
const selectedCountryCode = ref<CountryCode>(props.defaultCountry);

// Find selected country data
const selectedCountryData = computed(() => {
  return availableCountries.value.find(
    (country) => country.code === selectedCountryCode.value
  );
});

// Filter countries based on search
const filteredCountries = computed(() => {
  if (!searchQuery.value) {
    return availableCountries.value;
  }
  return availableCountries.value.filter(
    (country) =>
      country.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      country.code.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      country.dialCode.includes(searchQuery.value)
  );
});

// Inject form context if available
const formContext = inject("formContext", null);

// Check if we should use vee-validate (when inside BForm and name is provided)
const shouldUseVeeValidate = formContext && props.name;

// Setup vee-validate field if inside a form
const veeField = shouldUseVeeValidate
  ? useField(props.name, undefined, {
      syncVModel: true,
      initialValue: props.modelValue || "",
    })
  : null;

// Use vee-validate value and error if available, otherwise use internal state
const internalValueRef = shouldUseVeeValidate ? null : ref(props.modelValue);
const internalError = ref<string>("");

// Computed property for the current value
const internalValue = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return veeField.value.value;
  }
  return internalValueRef?.value;
});

// Parse phone number and provide formatting
const parsedPhone = computed(() => {
  if (!internalValue.value) return null;
  return parsePhoneNumberFromString(
    internalValue.value,
    selectedCountryCode.value
  );
});

// Display formatted value
const displayValue = computed(() => {
  if (!internalValue.value) return "";

  if (props.formatAsYouType) {
    return formatIncompletePhoneNumber(
      internalValue.value,
      selectedCountryCode.value
    );
  }

  return internalValue.value;
});

const onFocus = () => {};

const onKeyDown = (event: KeyboardEvent) => {
  // Allow only numbers, +, -, space, parentheses, and control keys
  const allowedKeys = [
    "Backspace",
    "Delete",
    "Tab",
    "Escape",
    "Enter",
    "Home",
    "End",
    "ArrowLeft",
    "ArrowRight",
    "ArrowUp",
    "ArrowDown",
  ];

  const isNumberKey = /^[0-9]$/.test(event.key);
  const isSpecialChar = /^[\+\-\s\(\)]$/.test(event.key);
  const isAllowedKey = allowedKeys.includes(event.key);
  const isCtrlCmd = event.ctrlKey || event.metaKey;

  if (!isNumberKey && !isSpecialChar && !isAllowedKey && !isCtrlCmd) {
    event.preventDefault();
  }
};

const hasError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const displayError = computed(() => {
  if (shouldUseVeeValidate && veeField) {
    return (
      props.errorMessage || veeField.errorMessage.value || internalError.value
    );
  }
  return props.errorMessage || internalError.value;
});

const emit = defineEmits([
  "update:modelValue",
  "change",
  "blur",
  "tabPressed",
  "enterPressed",
  "phoneValidated",
  "countryChanged",
]);

const validatePhoneNumber = (phoneNumber: string) => {
  if (!phoneNumber) {
    internalError.value = "";
    return;
  }

  const parsed = parsePhoneNumberFromString(
    phoneNumber,
    selectedCountryCode.value
  );

  if (!parsed) {
    internalError.value = "Invalid phone number format";
    emit("phoneValidated", { isValid: false, phone: null });
    return;
  }

  if (!parsed.isValid()) {
    internalError.value = "Please enter a valid phone number";
    emit("phoneValidated", { isValid: false, phone: parsed });
    return;
  }

  internalError.value = "";
  emit("phoneValidated", { isValid: true, phone: parsed });

  // Emit country detection if it differs from selected
  if (parsed.country && parsed.country !== selectedCountryCode.value) {
    // Auto-update country if detected from phone number
    selectedCountryCode.value = parsed.country;
    emit("countryChanged", parsed.country);
  }
};

const updateValue = (event: Event) => {
  const rawValue = (event.target as HTMLInputElement).value;

  // Store the raw value (without formatting) as the actual value
  let cleanValue = rawValue.replace(/[^\d\+]/g, "");

  if (shouldUseVeeValidate && veeField) {
    // If using vee-validate, update the field value
    veeField.value.value = cleanValue;
  } else {
    // Traditional approach
    if (internalValueRef) {
      internalValueRef.value = cleanValue;
    }

    // Clear error when user starts typing (if there's an error)
    if (internalError.value && cleanValue.length > 0) {
      internalError.value = "";
    }
  }

  // Validate phone number
  validatePhoneNumber(cleanValue);

  emit("update:modelValue", cleanValue);
  emit("change", cleanValue);
};

// Country dropdown methods
const toggleCountryDropdown = () => {
  dropdownOpen.value = !dropdownOpen.value;
  if (dropdownOpen.value) {
    nextTick(() => {
      searchInput.value?.focus();
    });
  }
};

const closeDropdown = () => {
  dropdownOpen.value = false;
  searchQuery.value = "";
};

const selectCountry = (country: any) => {
  selectedCountryCode.value = country.code;
  closeDropdown();
  emit("countryChanged", country.code);

  // Re-validate current phone number with new country
  if (internalValue.value) {
    validatePhoneNumber(internalValue.value);
  }

  // Focus back to phone input
  nextTick(() => {
    inputElement.value?.focus();
  });
};

// Close dropdown when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest(".ff_input")) {
    closeDropdown();
  }
};

const emitBlur = () => {
  // Validate on blur
  if (internalValue.value) {
    validatePhoneNumber(internalValue.value);
  }
  emit("blur");
};

const onTabPressed = (event: any) => {
  emit("tabPressed", event);
};

const onEnterPressed = (event: any) => {
  emit("enterPressed", event);
};

watch(
  () => props.modelValue,
  (newValue) => {
    if (shouldUseVeeValidate && veeField) {
      veeField.value.value = newValue || "";
    } else {
      if (internalValueRef) {
        internalValueRef.value = newValue;
      }
    }
  }
);

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

defineExpose({ focus, clearValidationError });
</script>

<style scoped lang="scss">
.ff_input {
  position: relative;
}

.ff-input-container {
  position: relative;

  &.input-with-left-icon {
    .left-icon {
      position: absolute;
      height: 100%;
      display: flex;
      align-items: center;
      width: 70px;
      justify-content: center;
      left: 0;
      top: 0;

      &.cursor {
        cursor: pointer;
      }
    }

    input {
      padding-left: 100px;
      font-family: var(--font-2);
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
    }
  }

  &.has-country-dropdown {
    z-index: 1001;
  }
}

// Custom styling for the country selector icon
.country-selector-icon {
  border-right: 1px solid #e5e7eb;
  padding-right: 10px;
  font-family: var(--font-2);
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;

  .country-display {
    display: flex;
    align-items: center;
    gap: 4px;
    justify-content: center;
  }

  .country-flag {
    font-size: 16px;
    line-height: 1;
  }

  .country-code {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    font-family: "SF Mono", "Monaco", "Inconsolata", "Fira Code", "Fira Mono",
      "Droid Sans Mono", "Consolas", monospace;
  }
}

.country-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  max-height: 320px;
  overflow: hidden;
  margin-top: 4px;
}

.search-container {
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  background: #fafbfc;
}

.country-search {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  font-family: var(--font-2);
  outline: none;
  transition: all 0.2s ease;

  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
}

.countries-list {
  max-height: 260px;
  overflow-y: auto;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

.country-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
  border-bottom: 1px solid #f8fafc;

  &:hover {
    background-color: #f8fafc;
  }

  &:last-child {
    border-bottom: none;
  }

  .country-flag {
    font-size: 20px;
    width: 28px;
    display: flex;
    justify-content: center;
  }

  .country-name {
    flex: 1;
    font-size: 14px;
    font-family: var(--font-2);
    color: #374151;
    font-weight: 500;
  }

  .country-dial-code {
    font-size: 13px;
    color: #6b7280;
    font-weight: 600;
    font-family: "SF Mono", "Monaco", "Inconsolata", "Fira Code", "Fira Mono",
      "Droid Sans Mono", "Consolas", monospace;
  }
}

.phone-info {
  margin-top: 0.25rem;

  .phone-type {
    font-size: 0.75rem;
    font-family: var(--font-2);
    color: #10b981;
    margin: 0;
  }
}
</style>
