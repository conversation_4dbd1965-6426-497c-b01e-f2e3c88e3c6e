<template>
  <div class="experience-selector">
    <label v-if="label" class="form-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>
    <div class="experience-selector-buttons">
      <button
        type="button"
        class="experience-btn"
        @click="handleExperienceDecrement"
        :disabled="modelValue <= 0"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M4 8H12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          />
        </svg>
      </button>
      <span class="experience-value">{{ modelValue }}+</span>
      <button
        type="button"
        class="experience-btn"
        @click="handleExperienceIncrement"
      >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path
            d="M8 4V12M4 8H12"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, inject } from "vue";
import { useField } from "vee-validate";

interface Props {
  modelValue?: number; // Expects "CURRENCY:FROM-TO"
  name?: string;
  label?: string;
  required?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 0,
  name: "",
});

// BForm integration
const formContext = inject("formContext", null);
const shouldUseVeeValidate = formContext && props.name;

const {
  value: experience,
  errorMessage,
  handleChange,
} = useField<number>(props.name, undefined, {
  initialValue: props.modelValue,
  syncVModel: true,
});

const handleExperienceIncrement = () => {
  experience.value++;
};

const handleExperienceDecrement = () => {
  experience.value--;
};

const emit = defineEmits<{
  "update:modelValue": [value: number];
}>();

watch(experience, (newVal) => {
  emit("update:modelValue", newVal);
});
</script>

<style scoped lang="scss">
.experience-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem 0;
  justify-content: space-between;

  .experience-selector-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .experience-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 50%;
    background-color: white;
    color: #6b7280;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover:not(:disabled) {
      border-color: var(--color--primary, #3b82f6);
      color: var(--color--primary, #3b82f6);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .experience-value {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    min-width: 3rem;
    text-align: center;
  }
}
</style>
