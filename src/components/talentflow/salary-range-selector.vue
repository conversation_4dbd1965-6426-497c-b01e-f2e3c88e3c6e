<template>
  <div class="salary-range-selector">
    <label v-if="label" class="form-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>

    <div class="salary-inputs-container">
      <!-- From Input -->
      <div class="salary-input-wrapper">
        <div class="input-with-dropdown">
          <B_Input
            :model-value="fromAmount"
            @update:model-value="handleFromInput"
            placeholder="From"
            :is-money="true"
            validation-type="none"
          />
          <div
            class="currency-dropdown-overlay"
            @click="showFromCurrencyDropdown = !showFromCurrencyDropdown"
          >
            <span class="currency-text">{{ currency }}</span>
            <svg
              class="chevron-icon"
              width="12"
              height="8"
              viewBox="0 0 12 8"
              fill="none"
              :class="{ rotated: showFromCurrencyDropdown }"
            >
              <path
                d="M1 1.5L6 6.5L11 1.5"
                stroke="#667085"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Currency Dropdown -->
          <div v-if="showFromCurrencyDropdown" class="currency-options">
            <div
              v-for="currencyOption in currencyOptions"
              :key="currencyOption.id"
              class="currency-option"
              @click="selectCurrency(currencyOption.id)"
            >
              {{ currencyOption.label }}
            </div>
          </div>
        </div>
      </div>

      <!-- To Input -->
      <div class="salary-input-wrapper">
        <div class="input-with-dropdown">
          <B_Input
            :model-value="toAmount"
            @update:model-value="handleToInput"
            placeholder="To"
            :is-money="true"
            validation-type="none"
          />
          <div
            class="currency-dropdown-overlay"
            @click="showToCurrencyDropdown = !showToCurrencyDropdown"
          >
            <span class="currency-text">{{ currency }}</span>
            <svg
              class="chevron-icon"
              width="12"
              height="8"
              viewBox="0 0 12 8"
              fill="none"
              :class="{ rotated: showToCurrencyDropdown }"
            >
              <path
                d="M1 1.5L6 6.5L11 1.5"
                stroke="#667085"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>

          <!-- Currency Dropdown -->
          <div v-if="showToCurrencyDropdown" class="currency-options">
            <div
              v-for="currencyOption in currencyOptions"
              :key="currencyOption.id"
              class="currency-option"
              @click="selectCurrency(currencyOption.id)"
            >
              {{ currencyOption.label }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Display a single error message for the whole component -->
    <div v-if="errorMessage" class="input-error">
      <p class="error-text">{{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, inject } from "vue";
import { useField } from "vee-validate";
import B_Input from "@/components/ff/input.vue";
import { useRegistrationStore } from "@/stores/registration.store";

interface Props {
  modelValue?: string; // Expects "CURRENCY:FROM-TO"
  name?: string;
  label?: string;
  required?: boolean;
  currencyOptions?: Array<{ id: string; label: string }>;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: "",
  name: "",
  currencyOptions: () => [
    { id: "USD", label: "USD" },
    { id: "EUR", label: "EUR" },
    { id: "GBP", label: "GBP" },
    { id: "CAD", label: "CAD" },
    { id: "AUD", label: "AUD" },
    { id: "JPY", label: "JPY" },
  ],
});

const emit = defineEmits<{
  "update:modelValue": [value: string];
  change: [value: string];
}>();

// Get registration store instance
const registrationStore = useRegistrationStore();

// BForm integration
const formContext = inject("formContext", null);
const shouldUseVeeValidate = formContext && props.name;

const {
  value: salaryString,
  errorMessage,
  handleChange,
} = useField<string>(props.name, undefined, {
  initialValue: props.modelValue,
  syncVModel: true,
});

// Internal state derived from the salary string
const currency = ref("USD");
const fromAmount = ref("");
const toAmount = ref("");

// Watch for direct changes to modelValue from the parent
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      // When the prop changes, update the vee-validate field
      handleChange(newVal);
    }
  },
  { immediate: true }
);

// Parse the salary string from vee-validate's value
watch(
  salaryString,
  (newVal) => {
    if (newVal && newVal.includes(":")) {
      const parts = newVal.split(":");
      currency.value = parts[0];
      const range = parts[1].split("-");
      if (range.length === 2) {
        fromAmount.value = range[0];
        toAmount.value = range[1];
      }
    } else {
      // Reset if format is invalid or empty
      currency.value = "USD"; // Default currency
      fromAmount.value = "";
      toAmount.value = "";
    }
  },
  { immediate: true }
);

// Update the full salary string whenever a part changes
const updateSalaryString = () => {
  if (fromAmount.value && toAmount.value) {
    const salaryValue = `${currency.value}:${fromAmount.value}-${toAmount.value}`;

    // Update vee-validate field for BForm
    handleChange(salaryValue);

    // Directly update the store
    registrationStore.updateJobPreferences("salary", salaryValue);
  }
};

// Dropdown states
const showFromCurrencyDropdown = ref(false);
const showToCurrencyDropdown = ref(false);

// Event handlers that now call updateSalaryString
const handleFromInput = (value: number | string) => {
  // Convert number to string for internal storage
  fromAmount.value = typeof value === "number" ? value.toString() : value;
  updateSalaryString();
};

const handleToInput = (value: number | string) => {
  // Convert number to string for internal storage
  toAmount.value = typeof value === "number" ? value.toString() : value;
  updateSalaryString();
};

const selectCurrency = (selectedCurrency: string) => {
  currency.value = selectedCurrency;
  showFromCurrencyDropdown.value = false;
  showToCurrencyDropdown.value = false;
  updateSalaryString();
};

// Expose meta for parent components if needed (though error is now handled by BForm)
defineExpose({
  // meta is no longer relevant here as validation is in the parent
});
</script>

<style scoped lang="scss">
.salary-range-selector {
  margin-bottom: 1.5rem;
  .form-label {
    font-family: var(--font-2);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    color: #000;
    line-height: 24px;
    display: inline-flex;
    margin-bottom: 0.5rem;

    .required {
      color: #ef4444;
    }
  }

  .salary-inputs-container {
    display: flex;
    gap: 1rem;
    align-items: flex-start;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0.75rem;
    }
  }

  .salary-input-wrapper {
    flex: 1;
    // Allow flexbox to shrink inputs
  }

  .input-with-dropdown {
    position: relative;

    .currency-dropdown-overlay {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0 5px;

      cursor: pointer;
      user-select: none;
      min-width: 80px;
      justify-content: center;
      border-radius: 0 6px 6px 0;

      .currency-text {
        font-family: var(--font-2);
        font-size: 14px;

        color: #374151;
      }

      .chevron-icon {
        transition: transform 0.2s ease;
        flex-shrink: 0;

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }

    // Override the input to make space for currency dropdown
    :deep(.ff-input-container) {
      input {
        padding-right: 100px !important;
      }
    }

    .currency-options {
      position: absolute;
      top: 100%;
      right: 0;
      width: 120px;
      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0px 6px 12px rgba(18, 18, 18, 0.05),
        0px 1px 1px rgba(18, 18, 18, 0.1), 0px 0px 0px 1px rgba(18, 18, 18, 0.1);
      z-index: 1000;
      max-height: 200px;
      overflow-y: auto;

      .currency-option {
        padding: 0.75rem 1rem;
        cursor: pointer;
        font-family: var(--font-2);
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        border-bottom: 1px solid #f3f4f6;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f9fafb;
        }
      }
    }
  }

  .input-error {
    margin-top: 0.5rem;

    .error-text {
      color: #ef4444;
      font-size: 0.875rem;
      font-weight: 500;
      margin: 0;
    }
  }
}
</style>
