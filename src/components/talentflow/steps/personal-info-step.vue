<template>
  <div class="step-form">
    <div class="form-header">
      <h1 class="form-title">Let's get to know you</h1>
      <p class="form-subtitle">
        Start with your name and where you're from — just the basics.
      </p>
    </div>

    <BForm
      :validation-schema="personalInfoSchema"
      :initial-values="{
        name: formData.name,
        country: formData.country,
        phoneNumber: formData.phoneNumber,
      }"
      @validation-change="handleValidationChange"
      class="onboarding-form"
    >
      <!-- LinkedIn Button -->
      <!-- <B_Button
        type="secondary"
        size="lg"
        :is-box="false"
        :fullwidth="true"
        leftIcon="linkedin"
        class="linkedin-btn"
      >
        Fill in with LinkedIn
      </B_Button> -->

      <!-- Divider -->
      <!-- <Divider text="or" class="form-divider" /> -->

      <!-- Name Input -->
      <div class="form-group">
        <B_Input
          label="What should we call you?"
          name="name"
          placeholder="e.g. <PERSON>"
          required
          :model-value="formData.name"
          @update:model-value="handleNameChange"
        />
      </div>

      <!-- Country Selector -->
      <div class="form-group">
        <SearchInput
          :left-icon="'mapPin'"
          label="Select your country"
          name="country"
          :model-value="formData.country"
          @update:model-value="handleCountryChange"
          placeholder="e.g. United States"
          :required="true"
          :countChars="true"
          :max-chars="200"
          :min-characters="2"
        />
      </div>

      <!-- Phone Number -->
      <div class="form-group">
        <PhoneInput
          label="Phone number"
          name="phoneNumber"
          placeholder="e.g. +****************"
          :model-value="formData.phoneNumber"
          @phone-validated="handlePhoneValidation"
          @country-detected="handleCountryDetection"
        />
      </div>
    </BForm>

    <!-- Form Actions -->
    <div class="form-actions">
      <B_Button
        type="primary"
        size="lg"
        :is-box="false"
        :fullwidth="true"
        :disabled="!isFormValid"
        @click="handleSubmit"
      >
        Continue
      </B_Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import SearchInput from "@/components/talentflow/search-input.vue";
import PhoneInput from "@/components/talentflow/phone-input.vue";
import { ref } from "vue";

import B_Input from "@/components/ff/input.vue";
import B_Button from "@/components/talentflow/button.vue";
import BForm from "@/components/ff/form.vue";

import { useRegistrationStore } from "@/stores/registration.store";
import { personalInfoSchema } from "@/utils/validation-schemas/onboarding/personalInfoSchema";
import { useAuthStore } from "@/stores/auth.store";
const authStore = useAuthStore();
const registrationStore = useRegistrationStore();
const emit = defineEmits<{ validationChange: [isValid: boolean] }>();

// The BForm's validation status is now the single source of truth for this step's validity.
const isFormValid = ref(false);

const handleValidationChange = (isValid: boolean) => {
  isFormValid.value = isValid;
  emit("validationChange", isValid);
};

// These handlers are still needed to update the store,
// but they no longer need to call checkOverallValidity().
const handleCountryChange = (value: string) => {
  registrationStore.updatePersonalInfo("country", value);
  formData.value.country = value;
};

const handleCountrySelection = (country: any) => {
  // registrationStore.setSelectedCountry(country);
  console.log(country);
};

const handlePhoneValidation = (validation: {
  isValid: boolean;
  phone: any;
}) => {
  registrationStore.setPhoneValidation(validation.isValid);
  // Also save the phone number to store
  if (validation.phone) {
    registrationStore.updatePersonalInfo("phoneNumber", validation.phone);
    formData.value.phoneNumber = validation.phone;
  }
  console.log("Phone validation:", validation);
};

const handleCountryDetection = (countryCode: string) => {
  // Optionally update selected country when detected from phone number
  console.log("Country detected from phone:", countryCode);
};

const handleNameChange = (value: string) => {
  registrationStore.updatePersonalInfo("name", value);
  formData.value.name = value;
};

// Expose submitForm for the parent component, which now just relies on BForm's state.
const submitForm = () => {
  return isFormValid.value;
};

// Handle form submission and navigation
const handleSubmit = () => {
  if (submitForm()) {
    // Ensure all current form values are saved to store before navigation
    console.log("📋 Submitting personal info:", formData.value);

    registrationStore.updatePersonalInfo("name", formData.value.name);
    registrationStore.updatePersonalInfo("country", formData.value.country);
    registrationStore.updatePersonalInfo(
      "phoneNumber",
      formData.value.phoneNumber
    );

    // Log store state after update
    console.log(
      "🏪 Store personal info after update:",
      registrationStore.personalInfo
    );

    // Navigate to next step
    registrationStore.goToNextStep();
  } else {
    console.warn("⚠️ Form validation failed, cannot submit");
  }
};

defineExpose({
  submitForm,
  isValid: isFormValid, // Expose the raw ref
});

const formData = ref({
  name: authStore.user?.fullName || "",
  country: "",
  phoneNumber: "",
})

</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;

.step-form {
  border-radius: 12px;
  padding: 3rem;
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;

  .form-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .form-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    line-height: 1.6;
  }
}

.onboarding-form {
  .linkedin-btn {
    margin-bottom: 1.5rem;
    display: flex !important;
    width: 100% !important;
  }

  .form-divider {
    margin: 1.5rem 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-actions {
    padding-top: 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

// Responsive design
@media (max-width: 768px) {
  .step-form {
    padding: 2rem 1.5rem;
  }

  .form-header {
    .form-title {
      font-size: 1.5rem;
    }

    .form-subtitle {
      font-size: 1rem;
    }
  }
}
</style>
