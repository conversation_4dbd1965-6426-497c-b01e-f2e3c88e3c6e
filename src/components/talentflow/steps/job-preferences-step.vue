<template>
  <div class="step-form">
    <div class="form-header">
      <h1 class="form-title">Let's Talk About Your Dream Role</h1>
      <p class="form-subtitle">
        We want to match you with roles that truly excite you, tell us what
        you're looking for.
      </p>
    </div>

    <BForm
      :validation-schema="jobPreferencesSchema"
      :initial-values="{
        dreamJob: formData.dreamJob,
        preferedLocation: formData.preferedLocation,
        experience: formData.experience,
        workSetup: formData.workSetup,
        salary: formData.salary,
      }"
      @validation-change="handleValidationChange"
      class="onboarding-form"
    >
      <!-- Dream Job Input -->
      <div class="form-group">
        <SearchInput
          :left-icon="'job'"
          label="What's your dream job?"
          name="dreamJob"
          :model-value="formData.dreamJob"
          @update:model-value="handleDreamJobChange"
          searchType="jobPosition"
          placeholder="e.g. <PERSON><PERSON>eloper, Marketing Manager, UX Designer"
          :required="true"
          help-text="Be as specific or broad as you'd like — we'll refine it later."
          :show-suggestions-on-focus="true"
        />
      </div>

      <!-- Location Input -->
      <div class="form-group">
        <SearchInput
          :left-icon="'mapPin'"
          label="Where would you love to work?"
          name="preferedLocation.formattedAddress"
          :model-value="formData.preferedLocation.formattedAddress"
          @update:model-value="handlePreferedLocationChange"
          placeholder="e.g. United States"
          :required="true"
          :min-characters="2"
        />
      </div>

      <!-- Desired Salary -->
      <div class="form-group">
        <SalaryRangeSelector
          label="What's your desired salary?"
          name="salary"
          :model-value="formData.salary"
          :required="true"
          @update:model-value="handleSalaryChange"
        />
      </div>

      <!-- Years of Experience -->
      <div class="form-group">
        <ExperienceSelector
          label="Years of experience"
          name="experience"
          :model-value="formData.experience"
        />
      </div>

      <!-- Work Setup -->
      <WorkSetupSelect
        label="What's your ideal work setup?"
        name="workSetup"
        :model-value="formData.workSetup"
        @update:model-value="handleWorkSetupChange"
      />
    </BForm>

    <!-- Form Actions -->
    <div class="form-actions">
      <B_Button
        type="secondary"
        size="lg"
        :is-box="false"
        leftIcon="arrowLeft"
        @click="handleBack"
        class="back-btn"
      >
        Back
      </B_Button>

      <B_Button
        type="primary"
        size="lg"
        :is-box="false"
        :disabled="!isAllValid"
        @click="handleSubmit"
        class="submit-btn"
      >
        Let's Get Your Resume Ready
      </B_Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import SearchInput from "@/components/talentflow/search-input.vue";
import WorkSetupSelect from "@/components/talentflow/work-setup-select.vue";
import SalaryRangeSelector from "@/components/talentflow/salary-range-selector.vue";
import B_Button from "@/components/talentflow/button.vue";
import BForm from "@/components/ff/form.vue";
import { useRegistrationStore } from "@/stores/registration.store";
import { jobPreferencesSchema } from "@/utils/validation-schemas/onboarding/jobPreferencesSchema";
import ExperienceSelector from "@/components/talentflow/experience-selector.vue";

const registrationStore = useRegistrationStore();

// Initialize form data from store
const formData = ref({
  dreamJob: "",
  preferedLocation: {
    formattedAddress: "",
  },
  workSetup: "",
  salary: "",
  experience: 0,
});

// Form validity state
const isFormValid = ref(false);
const isLocationValid = ref(false);

// Combined validation state
const isAllValid = computed(() => {
  // BForm handles validation for all fields except Location, which is custom.
  // Therefore, the form is fully valid if BForm is valid AND our manual location check is valid.
  const result = isFormValid.value && isLocationValid.value;

  console.log("🔍 isAllValid check (simplified):", {
    isFormValid: isFormValid.value,
    isLocationValid: isLocationValid.value,
    result,
  });

  return result;
});

const handleSubmit = () => {
  console.log("🔍 formData:", formData.value);
  if (submitForm()) {
    // Ensure all current form values are saved to store before navigation

    registrationStore.updateJobPreferences("dreamJob", formData.value.dreamJob);
    registrationStore.updateJobPreferences(
      "preferedLocation",
      formData.value.preferedLocation
    );
    registrationStore.updateJobPreferences(
      "workSetup",
      formData.value.workSetup
    );
    registrationStore.updateJobPreferences("salary", formData.value.salary);
    registrationStore.updateJobPreferences(
      "experience",
      formData.value.experience
    );

    // Log store state after update
    console.log(
      "🏪 Store job preferences after update:",
      registrationStore.jobPreferences
    );

    // Navigate to next step
    registrationStore.goToNextStep();
  } else {
    console.warn("⚠️ Form validation failed, cannot submit");
  }
};

// Event emitters
const emit = defineEmits<{
  validationChange: [isValid: boolean];
}>();

// Dream job method (simple - SearchInput handles the suggestions)
const handleDreamJobChange = (value: string) => {
  formData.value.dreamJob = value;
};

const handlePreferedLocationChange = (value: string) => {
  formData.value.preferedLocation.formattedAddress = value;
};

const handleWorkSetupChange = (value: string) => {
  formData.value.workSetup = value;
};

const handleSalaryChange = (value: string) => {
  formData.value.salary = value;
};

// Methods
const handleValidationChange = (isValid: boolean) => {
  isFormValid.value = isValid;
  console.log("📋 BForm validation changed:", isValid);
  console.log("📋 Current formData:", formData.value);
  emit("validationChange", isValid);
};

const submitForm = () => {
  return isAllValid.value;
};

// Handle navigation
const handleBack = () => {
  registrationStore.goToPreviousStep();
};

// Additional initialization on mount
// (Location value initialization is handled above)

// Expose methods to parent
defineExpose({
  submitForm,
  isValid: isAllValid,
});
</script>

<style scoped lang="scss">
@use "../../../assets/scss/mixin" as *;

.step-form {
  border-radius: 12px;
  padding: 3rem;
}

.form-header {
  margin-bottom: 2rem;
  text-align: center;

  .form-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
  }

  .form-subtitle {
    font-size: 1.125rem;
    color: #6b7280;
    line-height: 1.6;
  }
}

.onboarding-form {
  .form-group {
    margin-bottom: 1.5rem;

    .form-help-text-small {
      font-size: 0.75rem;
      color: #6b7280;
      margin-top: 0.25rem;
      margin-bottom: 0;
    }
  }

  // Job preferences specific styles
  .experience-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
    justify-content: space-between;

    .experience-selector-buttons {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .experience-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 2.5rem;
      height: 2.5rem;
      border: 1px solid #d1d5db;
      border-radius: 50%;
      background-color: white;
      color: #6b7280;
      transition: all 0.2s ease;
      cursor: pointer;

      &:hover:not(:disabled) {
        border-color: var(--color--primary, #3b82f6);
        color: var(--color--primary, #3b82f6);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .experience-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      min-width: 3rem;
      text-align: center;
    }
  }

  .required {
    color: #ef4444;
  }

  .input-error {
    margin-top: 0.5rem;

    .error-text {
      font-size: 0.875rem;
      color: #ef4444;
    }
  }

  .form-actions {
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    .back-btn {
      flex-shrink: 0;
      min-width: auto;
    }

    .submit-btn {
      flex: 1;
    }
  }
}

// Location field now uses SearchInput component - no custom styles needed

// Responsive design
@media (max-width: 768px) {
  .step-form {
    padding: 2rem 1.5rem;
  }

  .form-header {
    .form-title {
      font-size: 1.5rem;
    }

    .form-subtitle {
      font-size: 1rem;
    }
  }
}
</style>
