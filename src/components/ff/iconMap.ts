import {
  CheckIcon,
  XIcon,
  EyeOffIcon,
  EyeIcon,
  X,
  Lock,
  LogIn,
  LogOut,
  HelpCircle,
  ArrowLeft,
  DollarSignIcon,
  MapPinIcon,
  BriefcaseIcon,
} from "lucide-vue-next";
import GoogleIcon from "./icons/GoogleIcon.vue";
import LinkedinIcon from "./icons/LinkedinIcon.vue";

export interface IconProps {
  size?: number;
  strokeWidth?: number;
  color?: string;
  class?: string;
}

export const defaultIconProps: IconProps = {
  size: 20,
  strokeWidth: 2,
  class: "ff-icon",
};

export const iconMap = {
  check: CheckIcon,
  x: XIcon,
  eye: EyeIcon,
  eyeOff: EyeOffIcon,
  close: X,
  lock: Lock,
  login: LogIn,
  google: GoogleIcon,
  linkedin: LinkedinIcon,
  eyeClosed: EyeOffIcon,
  eyeOpen: EyeIcon,
  logout: LogOut,
  help: HelpCircle,
  arrowLeft: ArrowLeft,
  dollar: DollarSignIcon,
  mapPin: MapPinIcon,
  mapPin2: MapPinIcon,
  job: BriefcaseIcon,
  "": null,
} as const;

export type IconName = keyof typeof iconMap;
