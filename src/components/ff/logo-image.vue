<template>
  <div class="logo-container">
    <img
      :src="src"
      :width="logoWidth"
      :height="height"
      :alt="alt"
      :class="classes"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

interface Props {
  src?: string;
  size?: "sm" | "md" | "lg" | "xl" | "2xl";
  width?: string | number;
  height?: string | number;
  alt?: string;
  classes?: string;
}

const props = withDefaults(defineProps<Props>(), {
  src: "/logo-full.svg",
  size: "xl",
  alt: "Logo",
  classes: "",
});

// Size mapping
const sizeMap = {
  sm: 80,
  md: 100,
  lg: 120,
  xl: 140,
  "2xl": 160,
};

// Computed width - use custom width if provided, otherwise use size mapping
const logoWidth = computed(() => {
  if (props.width) {
    return props.width;
  }
  return sizeMap[props.size];
});
</script>

<style scoped lang="scss">
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
}
</style>
