<template>
  <svg
    :width="size"
    :height="size"
    :class="className"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
     class="ff-custom-icon"
  >
    <g clip-path="url(#clip0_73_5163)">
      <path
        d="M15.3571 0H1.63929C1.01071 0 0.5 0.517857 0.5 1.15357V14.8464C0.5 15.4821 1.01071 16 1.63929 16H15.3571C15.9857 16 16.5 15.4821 16.5 14.8464V1.15357C16.5 0.517857 15.9857 0 15.3571 0ZM5.33571 13.7143H2.96429V6.07857H5.33929V13.7143H5.33571ZM4.15 5.03571C3.38929 5.03571 2.775 4.41786 2.775 3.66071C2.775 2.90357 3.38929 2.28571 4.15 2.28571C4.90714 2.28571 5.525 2.90357 5.525 3.66071C5.525 4.42143 4.91071 5.03571 4.15 5.03571ZM14.225 13.7143H11.8536V10C11.8536 9.11429 11.8357 7.975 10.6214 7.975C9.38571 7.975 9.19643 8.93929 9.19643 9.93571V13.7143H6.825V6.07857H9.1V7.12143H9.13214C9.45 6.52143 10.225 5.88929 11.3786 5.88929C13.7786 5.88929 14.225 7.47143 14.225 9.52857V13.7143Z"
        fill="#2867B2"
      />
    </g>
    <defs>
      <clipPath id="clip0_73_5163">
        <rect width="16" height="16" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
</template>

<script setup lang="ts">
defineProps<{
  size?: number;
  className?: string;
}>();
</script>
