export interface Country {
  id: string;
  name: string;
  code?: string;
  flag?: string;
  dialCode?: string;
}

// New type for the actual API response
export interface LocationSearchResult {
  placeId: string;
  name: string;
  country: {
    code: string;
    name: string;
  };
  city: string;
  district: string;
  zipCode: string;
  formattedAddress: string;
  lanAndlng: {
    lat: number;
    lng: number;
  };
}

export interface LocationSearchResponse {
  success: boolean;
  message: string;
  data: LocationSearchResult[];
}
