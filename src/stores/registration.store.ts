import { defineStore } from "pinia";
import type { Country, LocationSearchResult } from "./types/country.types";
import type { JobSeekerProfile } from "./types/auth.types";

// Minimal interfaces for registration-specific data
export interface PersonalInfo {
  name: string;
  country: string;
  phoneNumber: string;
}

export interface JobPreferences {
  dreamJob: string;
  preferedLocation: LocationSearchResult;
  salary: string;
  experience: number;
  workSetup: JobSeekerProfile["workSetup"];
}

export interface Step {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

export interface RegistrationState {
  // Form data
  personalInfo: PersonalInfo;
  jobPreferences: JobPreferences;

  // Selected country for phone input
  selectedCountry: Country | null;

  // Phone validation status
  isPhoneValid: boolean;

  // Step management
  steps: Step[];
  currentStep: string;
}

export const useRegistrationStore = defineStore("registration", {
  state: (): RegistrationState => ({
    // Form data
    personalInfo: {
      name: "",
      country: "",
      phoneNumber: "",
    },
    jobPreferences: {
      dreamJob: "",
      preferedLocation: {
        placeId: "",
        name: "",
        country: {
          code: "",
          name: "",
        },
        city: "",
        district: "",
        zipCode: "",
        formattedAddress: "",
        lanAndlng: {
          lat: 0,
          lng: 0,
        },
      },
      salary: "",
      experience: 2,
      workSetup: "on-site",
    },

    // Selected country
    selectedCountry: null,

    // Phone validation status
    isPhoneValid: false,

    // Steps
    steps: [
      {
        id: "personal-info",
        title: "Let's get to know you",
        description:
          "Let's start with the basics — your name, location, and how to stay in touch.",
        completed: false,
      },
      {
        id: "job-preferences",
        title: "Your Dream Job, Your Rules",
        description:
          "Tell us what you're aiming for title, location, remote vibes — we've got your back.",
        completed: false,
      },
    ],
    currentStep: "personal-info",
  }),

  getters: {
    currentStepIndex(): number {
      return this.steps.findIndex((step) => step.id === this.currentStep);
    },

    isLastStep(): boolean {
      return this.currentStepIndex === this.steps.length - 1;
    },
  },

  actions: {
    // Personal info actions
    updatePersonalInfo(field: keyof PersonalInfo, value: string) {
      this.personalInfo[field] = value;
    },

    // Job preferences actions
    updateJobPreferences(field: keyof JobPreferences, value: any) {
      (this.jobPreferences as any)[field] = value;
    },

    decrementExperience() {
      if (this.jobPreferences.experience > 0) {
        this.jobPreferences.experience--;
      }
    },

    setSelectedLocation(location: LocationSearchResult) {
      this.jobPreferences.preferedLocation = location;
    },

    setPhoneValidation(isValid: boolean) {
      this.isPhoneValid = isValid;
    },

    // Step management actions
    goToNextStep() {
      const currentIndex = this.currentStepIndex;
      if (currentIndex < this.steps.length - 1) {
        // Mark current step as completed
        this.steps[currentIndex].completed = true;
        // Move to next step
        this.currentStep = this.steps[currentIndex + 1].id;
      }
    },

    goToPreviousStep() {
      const currentIndex = this.currentStepIndex;
      if (currentIndex > 0) {
        this.currentStep = this.steps[currentIndex - 1].id;
      }
    },

    // Form submission actions (validation should be handled in component)
    submitPersonalInfo(): boolean {
      this.goToNextStep();
      return true;
    },

    // Reset store
    resetRegistration() {
      this.personalInfo = {
        name: "",
        country: "",
        phoneNumber: "",
      };
      this.jobPreferences = {
        dreamJob: "",
        preferedLocation: {
          placeId: "",
          name: "",
          country: {
            code: "",
            name: "",
          },
          city: "",
          district: "",
          zipCode: "",
          formattedAddress: "",
          lanAndlng: {
            lat: 0,
            lng: 0,
          },
        },
        salary: "",
        experience: 2,
        workSetup: "on-site",
      };
      this.selectedCountry = null;
      this.isPhoneValid = false;
      this.currentStep = "personal-info";
      this.steps.forEach((step) => (step.completed = false));
    },
  },

  persist: true,
});
