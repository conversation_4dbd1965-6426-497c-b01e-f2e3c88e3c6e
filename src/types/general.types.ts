export type ProviderLoginTypes = "linkedin" | "google";
export type Role = "JOBSEEKER" | "RECRUITER" | "ADMIN";
export type RoleShortened = "js" | "hr" | "admin";


export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}


export type SearchType = "dreamRoles" | "places";