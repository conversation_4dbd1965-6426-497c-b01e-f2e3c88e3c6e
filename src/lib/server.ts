import axios from "axios";
import { AuthService } from "@/services/auth.service";

// Create a custom error object with proper typing
interface CustomError extends Error {
  isRateLimit: boolean;
  originalError: any;
}
import emitter from "@events";
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:8080/",
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.log("#### AXIOS ERROR ####");
    console.log(error);
    const originalRequest = error.config;
    console.log(originalRequest);
    // Initialize _retry if it doesn't exist
    if (!originalRequest._retry) {
      originalRequest._retry = false;
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Attempt to refresh the token
        const response = await AuthService.refresh_token();

        // If refresh was successful, retry the original request
        if (response.success) {
          return api(originalRequest);
        }
      } catch (refreshError) {
        // If refresh token fails, clear auth and redirect to login
        await AuthService.logout();
      }
    }
    if (error.code === "ERR_NETWORK" && error.config) {
      const customError = Object.assign(
        new Error("Request failed - possibly rate limited"),
        {
          isRateLimit: true,
          originalError: error,
        }
      ) as CustomError;

      emitter.emit("show-toast", {
        type: "error",
        title:
          "Whoa there, cowboy! 🤠 You're moving too fast! Take a breather and try again in a minute.",
      });

      return Promise.reject(customError);
    }

    return Promise.reject(error);
  }
);

export { api };
